<!-- Pop-up create-trip-modal -->
<div id="create-trip-modal" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 hidden"
    onclick="closeModal('create-trip-modal')">
    <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 overflow-y-auto max-h-screen"
        onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-800">Proposer un trajet</h2>
            <button onclick="closeModal('create-trip-modal')"
                class="text-gray-500 hover:text-gray-800 text-3xl leading-none">&times;</button>
        </div>

        <!-- Body -->
        <form id="createTripForm" action="{{ route('covoiturage.store') }}" method="POST">
            @csrf

            <h3 class="text-xl font-semibold text-gray-700 mb-2">Informations du trajet</h3>
            <p class="text-sm text-slate-600 mb-6">Veuillez remplir tous les champs correctement. Le prix sera
                automatiquement majoré de 2 crédits pour la plateforme.</p>

            <!-- Adresses de départ et d'arrivée -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Adresse de départ -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-700 border-b pb-2">Départ</h4>
                    <div>
                        <label for="departure_address" class="block font-semibold text-gray-700">Adresse de départ
                            *</label>
                        <input type="text" id="departure_address" name="departure_address" maxlength="120" required
                            class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                            placeholder="Ex: 14 Boulevard Haussmann">
                        <small class="text-slate-500">Maximum 120 caractères.</small>
                    </div>
                    <div>
                        <label for="add_dep_address" class="block font-semibold text-gray-700">Complément
                            d'adresse</label>
                        <input type="text" id="add_dep_address" name="add_dep_address" maxlength="120"
                            class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                            placeholder="Ex: Bâtiment A, Porte 2">
                        <small class="text-slate-500">Optionnel. Maximum 120 caractères.</small>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="postal_code_dep" class="block font-semibold text-gray-700">Code postal *</label>
                            <input type="text" id="postal_code_dep" name="postal_code_dep" maxlength="6" required
                                class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                                placeholder="75002">
                            <small class="text-slate-500">Maximum 6 caractères.</small>
                        </div>
                        <div>
                            <label for="city_dep" class="block font-semibold text-gray-700">Ville *</label>
                            <input type="text" id="city_dep" name="city_dep" maxlength="120" required
                                class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                                placeholder="PARIS">
                            <small class="text-slate-500">Maximum 120 caractères.</small>
                        </div>
                    </div>
                </div>

                <!-- Adresse d'arrivée -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-700 border-b pb-2">Arrivée</h4>
                    <div>
                        <label for="arrival_address" class="block font-semibold text-gray-700">Adresse d'arrivée
                            *</label>
                        <input type="text" id="arrival_address" name="arrival_address" maxlength="120" required
                            class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                            placeholder="Ex: 8 Rue de Rome">
                        <small class="text-slate-500">Maximum 120 caractères.</small>
                    </div>
                    <div>
                        <label for="add_arr_address" class="block font-semibold text-gray-700">Complément
                            d'adresse</label>
                        <input type="text" id="add_arr_address" name="add_arr_address" maxlength="120"
                            class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                            placeholder="Ex: Entrée principale">
                        <small class="text-slate-500">Optionnel. Maximum 120 caractères.</small>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="postal_code_arr" class="block font-semibold text-gray-700">Code postal *</label>
                            <input type="text" id="postal_code_arr" name="postal_code_arr" maxlength="6" required
                                class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                                placeholder="13005">
                            <small class="text-slate-500">Maximum 6 caractères.</small>
                        </div>
                        <div>
                            <label for="city_arr" class="block font-semibold text-gray-700">Ville *</label>
                            <input type="text" id="city_arr" name="city_arr" maxlength="120" required
                                class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                                placeholder="MARSEILLE">
                            <small class="text-slate-500">Maximum 120 caractères.</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dates et heures -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div>
                    <label for="departure_date" class="block font-semibold text-gray-700">Date de départ *</label>
                    <input type="date" id="departure_date" name="departure_date" required min="{{ date('Y-m-d') }}"
                        class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1"
                        onchange="updateArrivalDate()">
                    <small class="text-slate-500">Ne peut pas être dans le passé.</small>
                    <!-- Champ caché pour arrival_date -->
                    <input type="hidden" id="arrival_date" name="arrival_date">
                </div>
                <div>
                    <label for="departure_time" class="block font-semibold text-gray-700">Heure de départ *</label>
                    <input type="time" id="departure_time" name="departure_time" required
                        class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1">
                </div>
                <div>
                    <label for="arrival_time" class="block font-semibold text-gray-700">Heure d'arrivée *</label>
                    <input type="time" id="arrival_time" name="arrival_time" required
                        class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1">
                    <small class="text-slate-500">Heure approximative.</small>
                </div>
                <div>
                    <label for="max_travel_time" class="block font-semibold text-gray-700">Durée max *</label>
                    <input type="time" id="max_travel_time" name="max_travel_time" required
                        class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1">
                    <small class="text-slate-500">Durée maximale du voyage.</small>
                </div>
            </div>

            <!-- Véhicule, places et prix -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label for="voiture_id" class="block font-semibold text-gray-700">Véhicule *</label>
                    <select id="voiture_id" name="voiture_id" required
                        class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1">
                        <option value="">Sélectionnez un véhicule</option>
                        @foreach ($voitures as $voiture)
                            <option value="{{ $voiture->voiture_id }}">
                                {{ $voiture->brand }} {{ $voiture->model }} ({{ $voiture->immat }}) -
                                {{ $voiture->n_place }} places
                            </option>
                        @endforeach
                    </select>
                    <small class="text-slate-500">Choisissez le véhicule pour ce trajet.</small>
                </div>
                <div>
                    <label for="n_tickets" class="block font-semibold text-gray-700">Nombre de places *</label>
                    <select id="n_tickets" name="n_tickets" required
                        class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1">
                        <option value="">Sélectionnez le nombre de places</option>
                        <option value="1">1 place</option>
                        <option value="2">2 places</option>
                        <option value="3">3 places</option>
                        <option value="4">4 places</option>
                        <option value="5">5 places</option>
                        <option value="6">6 places</option>
                        <option value="7">7 places</option>
                        <option value="8">8 places</option>
                    </select>
                    <small class="text-slate-500">Places disponibles pour les passagers.</small>
                </div>
                <div>
                    <label for="price" class="block font-semibold text-gray-700">Prix par place *</label>
                    <div class="relative">
                        <input type="number" id="price" name="price" min="1" max="200" required
                            class="w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm mt-1 pr-16"
                            placeholder="50">
                        <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">crédits</span>
                    </div>
                    <small class="text-slate-500">+ 2 crédits de commission automatique.</small>
                </div>
            </div>

            <!-- Option éco-responsable -->
            <div class="mb-6">
                <div class="flex items-center">
                    <input type="checkbox" id="eco_travel" name="eco_travel" value="1"
                        class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                    <label for="eco_travel" class="ml-2 block text-sm text-gray-700">
                        <span class="font-semibold">Trajet éco-responsable</span>
                        <span class="text-slate-500 block">Ce trajet respecte des critères environnementaux (véhicule
                            électrique/hybride, conduite éco, etc.)</span>
                    </label>
                </div>
            </div>

            <!-- Buttons -->
            <div class="flex justify-end space-x-4 pt-4 border-t">
                <button type="button" onclick="closeModal('create-trip-modal')"
                    class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-200">
                    Annuler
                </button>
                <button type="submit"
                    class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-200">
                    Proposer le trajet
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // Fonction pour mettre à jour la date d'arrivée
    function updateArrivalDate() {
        const departureDate = document.getElementById('departure_date').value;
        const arrivalDateField = document.getElementById('arrival_date');

        if (departureDate) {
            // Pour l'instant, on met la même date (trajet dans la journée)
            arrivalDateField.value = departureDate;
        }
    }

    // Validation du nombre de places selon le véhicule sélectionné
    document.addEventListener('DOMContentLoaded', function() {
        const voitureSelect = document.getElementById('voiture_id');
        const ticketsSelect = document.getElementById('n_tickets');

        if (voitureSelect && ticketsSelect) {
            voitureSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.value) {
                    // Extraire le nombre de places du texte de l'option
                    const text = selectedOption.text;
                    const placesMatch = text.match(/(\d+) places/);

                    if (placesMatch) {
                        const maxPlaces = parseInt(placesMatch[1]) - 1; // -1 pour le conducteur

                        // Vider et repeupler les options de places
                        ticketsSelect.innerHTML =
                            '<option value="">Sélectionnez le nombre de places</option>';

                        for (let i = 1; i <= Math.min(maxPlaces, 8); i++) {
                            const option = document.createElement('option');
                            option.value = i;
                            option.textContent = i + (i === 1 ? ' place' : ' places');
                            ticketsSelect.appendChild(option);
                        }
                    }
                }
            });
        }

        // Initialiser la date d'arrivée si une date de départ est déjà sélectionnée
        updateArrivalDate();
    });
</script>
