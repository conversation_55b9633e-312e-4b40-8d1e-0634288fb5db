<?php

namespace App\Http\Controllers;

use App\Http\Requests\DemandeRechercheCovoit;
use App\Http\Requests\StoreCovoiturageRequest;
use App\Models\Covoiturage;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class CovoitController extends Controller
{
    public function index()
    {
        return view('covoiturage');
    }

    public function search(DemandeRechercheCovoit $request)
    {
        $validated = $request->validated();

        // Pour le moment => données validées.
        // TODO => la recherche en base de données.
        dd($validated);
    }

    /** Création d'un nouveau covoiturage */
    public function store(StoreCovoiturageRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Ajouter l'ID de l'utilisateur connecté
        $validated['user_id'] = Auth::id();

        // Calculer la date d'arrivée (même jour par défaut)
        $validated['arrival_date'] = $validated['departure_date'];

        // Convertir la checkbox eco_travel en boolean
        $validated['eco_travel'] = $request->has('eco_travel') ? 1 : 0;

        // Valeurs par défaut pour les statuts
        $validated['trip_started'] = 0;
        $validated['trip_completed'] = 0;
        $validated['cancelled'] = 0;

        try {
            // Créer le covoiturage
            Covoiturage::create($validated);

            return Redirect::route('dashboard')->with('status', 'trip-created');
        } catch (\Exception $e) {
            return Redirect::route('dashboard')->with('error', 'Une erreur est survenue lors de la création du trajet. Veuillez réessayer.');
        }
    }
}
