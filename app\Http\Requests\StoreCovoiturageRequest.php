<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCovoiturageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // Adresses de départ
            'departure_address' => ['required', 'string', 'max:120'],
            'add_dep_address' => ['nullable', 'string', 'max:120'],
            'postal_code_dep' => ['required', 'string', 'max:6'],
            'city_dep' => ['required', 'string', 'max:120'],
            
            // Adresses d'arrivée
            'arrival_address' => ['required', 'string', 'max:120'],
            'add_arr_address' => ['nullable', 'string', 'max:120'],
            'postal_code_arr' => ['required', 'string', 'max:6'],
            'city_arr' => ['required', 'string', 'max:120'],
            
            // Dates et heures
            'departure_date' => ['required', 'date', 'after_or_equal:today'],
            'departure_time' => ['required', 'date_format:H:i'],
            'arrival_time' => ['required', 'date_format:H:i'],
            'max_travel_time' => ['required', 'date_format:H:i'],
            
            // Véhicule et places
            'voiture_id' => [
                'required', 
                'integer',
                Rule::exists('voiture', 'voiture_id')->where(function ($query) {
                    return $query->where('user_id', auth()->id());
                })
            ],
            'n_tickets' => ['required', 'integer', 'min:1', 'max:8'],
            
            // Prix
            'price' => ['required', 'integer', 'min:1', 'max:200'],
            
            // Option éco-responsable
            'eco_travel' => ['nullable', 'boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'departure_address.required' => 'L\'adresse de départ est obligatoire.',
            'departure_address.max' => 'L\'adresse de départ ne peut pas dépasser 120 caractères.',
            'add_dep_address.max' => 'Le complément d\'adresse de départ ne peut pas dépasser 120 caractères.',
            'postal_code_dep.required' => 'Le code postal de départ est obligatoire.',
            'postal_code_dep.max' => 'Le code postal de départ ne peut pas dépasser 6 caractères.',
            'city_dep.required' => 'La ville de départ est obligatoire.',
            'city_dep.max' => 'La ville de départ ne peut pas dépasser 120 caractères.',
            
            'arrival_address.required' => 'L\'adresse d\'arrivée est obligatoire.',
            'arrival_address.max' => 'L\'adresse d\'arrivée ne peut pas dépasser 120 caractères.',
            'add_arr_address.max' => 'Le complément d\'adresse d\'arrivée ne peut pas dépasser 120 caractères.',
            'postal_code_arr.required' => 'Le code postal d\'arrivée est obligatoire.',
            'postal_code_arr.max' => 'Le code postal d\'arrivée ne peut pas dépasser 6 caractères.',
            'city_arr.required' => 'La ville d\'arrivée est obligatoire.',
            'city_arr.max' => 'La ville d\'arrivée ne peut pas dépasser 120 caractères.',
            
            'departure_date.required' => 'La date de départ est obligatoire.',
            'departure_date.after_or_equal' => 'La date de départ ne peut pas être dans le passé.',
            'departure_time.required' => 'L\'heure de départ est obligatoire.',
            'departure_time.date_format' => 'L\'heure de départ doit être au format HH:MM.',
            'arrival_time.required' => 'L\'heure d\'arrivée est obligatoire.',
            'arrival_time.date_format' => 'L\'heure d\'arrivée doit être au format HH:MM.',
            'max_travel_time.required' => 'La durée maximale du voyage est obligatoire.',
            'max_travel_time.date_format' => 'La durée maximale doit être au format HH:MM.',
            
            'voiture_id.required' => 'Vous devez sélectionner un véhicule.',
            'voiture_id.exists' => 'Le véhicule sélectionné n\'existe pas ou ne vous appartient pas.',
            'n_tickets.required' => 'Le nombre de places est obligatoire.',
            'n_tickets.min' => 'Vous devez proposer au moins 1 place.',
            'n_tickets.max' => 'Vous ne pouvez pas proposer plus de 8 places.',
            
            'price.required' => 'Le prix par place est obligatoire.',
            'price.min' => 'Le prix minimum est de 1 crédit.',
            'price.max' => 'Le prix maximum est de 200 crédits.',
        ];
    }
}
