// ====================================================================================
// ==                                                                            ==
// ==                                 GESTION DES MODALES                        ==
// ==                                                                            ==
// ====================================================================================

function openModal(modalId) {
    console.log('Tentative d\'ouverture de la modale:', modalId); // Debug
    const modal = document.getElementById(modalId);
    if (modal) {
        console.log('Modale trouvée, ouverture...'); // Debug
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
    } else {
        console.error('Modale non trouvée:', modalId); // Debug
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }
}

window.openModal = openModal;
window.closeModal = closeModal;


// ====================================================================================
// ==                                                                            ==
// ==                             GESTION DES NOTIFICATIONS                      ==
// ==                                                                            ==
// ====================================================================================

function showSuccessNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-5 right-5 bg-green-500 text-white py-3 px-5 rounded-lg shadow-xl z-50 animate-bounce';
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-5 right-5 bg-red-500 text-white py-3 px-5 rounded-lg shadow-xl z-50 animate-bounce';
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

window.showSuccessNotification = showSuccessNotification;
window.showErrorNotification = showErrorNotification;


// ====================================================================================
// ==                                                                            ==
// ==                          LOGIQUE SPÉCIFIQUE AUX PAGES                      ==
// ==                                                                            ==
// ====================================================================================

document.addEventListener('DOMContentLoaded', () => {

    // ------------------------------------------------------------------------------------
    // --                      Gestionnaire d'événements générique pour ouvrir les modales --
    // ------------------------------------------------------------------------------------
    document.body.addEventListener('click', function(e) {
        // Chercher l'élément avec data-modal-target en remontant dans l'arbre DOM
        const target = e.target.closest('[data-modal-target]');
        if (target) {
            e.preventDefault(); // Empêcher le comportement par défaut
            const modalId = target.getAttribute('data-modal-target');
            console.log('Ouverture de la modale:', modalId); // Debug
            openModal(modalId);
        }
    });


    // ------------------------------------------------------------------------------------
    // --                            Fermeture des modales                             --
    // ------------------------------------------------------------------------------------
    const modals = document.querySelectorAll('.fixed.inset-0');
    modals.forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal(modal.id);
            }
        });
    });


    // ------------------------------------------------------------------------------------
    // --                         Modale Photo de Profil (popup.blade.php)               --
    // ------------------------------------------------------------------------------------
    const photoInput = document.getElementById('profile-photo-input');
    if (photoInput) {
        photoInput.addEventListener('change', function(event) {
            const previewArea = document.querySelector('.photo-preview-area');
            const file = event.target.files[0];
            if (file && previewArea) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewArea.innerHTML = `<img src="${e.target.result}" alt="Aperçu de la photo" class="h-full w-full object-cover">`;
                }
                reader.readAsDataURL(file);
            }
        });
    }

    const photoSubmitButton = document.getElementById('profile-photo-submit');
    if (photoSubmitButton) {
        photoSubmitButton.addEventListener('click', function() {
            const photoForm = document.getElementById('profilePhotoForm');
            if (photoForm) {
                photoForm.submit();
            }
        });
    }


    // ------------------------------------------------------------------------------------
    // --                       Modale Changement de Rôle (role.blade.php)               --
    // ------------------------------------------------------------------------------------
    const roleChangeForms = document.querySelectorAll('.role-change-form');
    roleChangeForms.forEach(form => {
        const warningElement = form.querySelector('#role-change-warning');

        form.addEventListener('submit', function(e) {
            const currentRole = this.dataset.currentRole;
            const newRole = this.querySelector('input[name="role"]:checked').value;

            if (currentRole === newRole) {
                e.preventDefault();
                if (warningElement) {
                    warningElement.classList.remove('hidden');
                }
                return;
            }

            if (warningElement) {
                warningElement.classList.add('hidden');
            }

            if (currentRole === 'Passager' && (newRole === 'Conducteur' || newRole === 'Les deux')) {
                e.preventDefault();
                const newRoleInput = document.getElementById('new_role_input');
                if (newRoleInput) {
                    newRoleInput.value = newRole;
                }
                openModal('driverinfo-modal');
            }
        });
    });


    // ------------------------------------------------------------------------------------
    // --                     Modale de Rechargement de Crédits (dashboard.blade.php)    --
    // ------------------------------------------------------------------------------------
    const rechargeModal = document.getElementById('recharge-modal');
    if (rechargeModal) {
        const validatePaymentBtn = document.getElementById('validate-payment-btn');
        const creditBalanceEl = document.getElementById('credit-balance');
        const amountOptions = document.querySelectorAll('input[name="recharge_amount"]');
        const warningEl = document.getElementById('payment-warning');
        const fakeInputs = rechargeModal.querySelectorAll('input[readonly]');
        const rechargeRoute = rechargeModal.dataset.rechargeUrl;
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        window.closeAndResetRechargeModal = function() {
            closeModal('recharge-modal');
            if (warningEl) warningEl.classList.add('hidden');
            if (validatePaymentBtn) validatePaymentBtn.disabled = true;
            const selectedOption = document.querySelector('.credit-option.border-green-500');
            if (selectedOption) {
                selectedOption.classList.remove('bg-green-100', 'border-green-500');
                const radio = selectedOption.querySelector('input[type="radio"]');
                if (radio) radio.checked = false;
            }
        }

        fakeInputs.forEach(input => {
            input.addEventListener('click', () => {
                if (warningEl) warningEl.classList.remove('hidden');
            });
        });

        amountOptions.forEach(option => {
            option.addEventListener('change', function() {
                if (this.checked) {
                    if (validatePaymentBtn) validatePaymentBtn.disabled = false;
                    document.querySelectorAll('.credit-option').forEach(label => label.classList.remove('bg-green-100', 'border-green-500'));
                    this.parentElement.classList.add('bg-green-100', 'border-green-500');
                }
            });
        });

        if (validatePaymentBtn) {
            validatePaymentBtn.addEventListener('click', function() {
                const selectedAmount = document.querySelector('input[name="recharge_amount"]:checked');
                if (!selectedAmount || !rechargeRoute) return;

                const amount = selectedAmount.value;
                this.disabled = true;

                fetch(rechargeRoute, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        },
                        body: JSON.stringify({
                            amount: amount
                        })
                    })
                    .then(response => response.ok ? response.json() : response.json().then(err => Promise.reject(err)))
                    .then(data => {
                        if (data.success && creditBalanceEl) {
                            creditBalanceEl.textContent = data.new_balance;
                            showSuccessNotification('Crédits rechargés avec succès !');
                        } else {
                            showErrorNotification(data.message || 'Une erreur est survenue.');
                        }
                        closeAndResetRechargeModal();
                    })
                    .catch(error => {
                        console.error('Erreur Fetch:', error);
                        showErrorNotification(error.message || 'Une erreur réseau est survenue.');
                        this.disabled = false;
                    });
            });
        }
    }

    // ------------------------------------------------------------------------------------
    // --                     Gestion de la modification de véhicule                     --
    // ------------------------------------------------------------------------------------
    document.body.addEventListener('click', function(e) {
        const editButton = e.target.closest('.edit-vehicle-btn');
        if (editButton) {
            const voitureData = JSON.parse(editButton.dataset.voiture);
            openEditVehicleModal(voitureData);
        }
    });
});


// ====================================================================================
// ==                                                                            ==
// ==                          GESTION DES VÉHICULES (CRUD)                      ==
// ==                                                                            ==
// ====================================================================================

function openEditVehicleModal(voiture) {
    document.getElementById('edit-brand').value = voiture.brand;
    document.getElementById('edit-model').value = voiture.model;
    document.getElementById('edit-immat').value = voiture.immat;
    document.getElementById('edit-date_first_immat').value = voiture.date_first_immat;
    document.getElementById('edit-color').value = voiture.color;
    document.getElementById('edit-n_place').value = voiture.n_place;
    document.getElementById('edit-energie').value = voiture.energie;

    const form = document.getElementById('editVehicleForm');
    form.action = `/voitures/${voiture.voiture_id}`;

    openModal('edit-vehicle-modal');
}
window.openEditVehicleModal = openEditVehicleModal;


let formToSubmit;
function confirmVehicleDeletion(event, vehicleCount) {
    event.preventDefault();
    formToSubmit = event.target;

    if (vehicleCount > 1) {
        if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
            formToSubmit.submit();
        }
    } else {
        openModal('delete-last-vehicle-modal');
        document.getElementById('confirm-delete-last-vehicle-btn').onclick = function() {
            formToSubmit.submit();
        };
    }
    return false;
}
window.confirmVehicleDeletion = confirmVehicleDeletion;
