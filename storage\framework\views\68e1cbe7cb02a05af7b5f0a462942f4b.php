<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Mon Espace')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php
                $userRole = Auth::user()->role;
            ?>

            <!-- Grand écran -->
            <div class="hidden md:grid md:grid-cols-3 gap-6">
                <!--Blocs Profil et Rôle -->
                <div class="md:col-span-2 md:row-span-1">
                    <?php echo $__env->make('dashboard.partials.profil', ['user' => $user], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
                <div class="md:col-start-3 md:row-span-2 h-full flex flex-col">
                    <div class="flex-grow h-full">
                        <?php echo $__env->make('dashboard.partials.role', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>

                <!--Blocs CHAUFFEUR -->
                <?php if($userRole === 'Conducteur'): ?>
                    <div class="md:col-span-2 md:row-start-2">
                        <?php echo $__env->make('dashboard.partials.preferences-conducteur', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-3">
                        <?php echo $__env->make('dashboard.partials.covoiturages-proposes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-4">
                        <?php echo $__env->make('dashboard.partials.mes-vehicules', ['voitures' => $voitures], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                <?php endif; ?>

                <!--Blocs PASSAGER -->
                <?php if($userRole === 'Passager'): ?>
                    <div class="md:col-span-2 md:row-start-2">
                        <?php echo $__env->make('dashboard.partials.reservations', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                <?php endif; ?>

                <!--Blocs LES DEUX -->
                <?php if($userRole === 'Les deux'): ?>
                    <div class="md:col-span-2 md:row-start-2">
                        <?php echo $__env->make('dashboard.partials.preferences-conducteur', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-3">
                        <?php echo $__env->make('dashboard.partials.reservations', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-4">
                        <?php echo $__env->make('dashboard.partials.covoiturages-proposes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="md:col-span-3 md:row-start-5">
                        <?php echo $__env->make('dashboard.partials.mes-vehicules', ['voitures' => $voitures], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                <?php endif; ?>

                <!--Blocs HISTORIQUE -->
                <div class="md:col-span-3">
                    <?php echo $__env->make('dashboard.partials.historique', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>

            <!-- Petit écran -->
            <div class="md:hidden space-y-6">
                <?php echo $__env->make('dashboard.partials.profil', ['user' => $user], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php echo $__env->make('dashboard.partials.role', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <?php if($userRole === 'Conducteur' || $userRole === 'Les deux'): ?>
                    <?php echo $__env->make('dashboard.partials.preferences-conducteur', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>

                <?php if($userRole === 'Passager' || $userRole === 'Les deux'): ?>
                    <?php echo $__env->make('dashboard.partials.reservations', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>

                <?php if($userRole === 'Conducteur' || $userRole === 'Les deux'): ?>
                    <?php echo $__env->make('dashboard.partials.covoiturages-proposes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <?php echo $__env->make('dashboard.partials.mes-vehicules', ['voitures' => $voitures], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>

                <?php echo $__env->make('dashboard.partials.historique', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>

    <?php echo $__env->make('dashboard.partials.popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.driverinfo-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.edit-preferences-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.add-vehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.edit-vehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.delete-last-vehicle-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('dashboard.partials.create-trip-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Recharge Modal -->
    <div id="recharge-modal" data-recharge-url="<?php echo e(route('credits.recharge')); ?>"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 hidden"
        onclick="closeModal('recharge-modal')">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4" onclick="event.stopPropagation()">
            <!-- Header -->
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold text-gray-800">Recharger vos crédits</h2>
                <button onclick="closeModal('recharge-modal')"
                    class="text-gray-500 hover:text-gray-800 text-3xl leading-none">&times;</button>
            </div>

            <!-- Body -->
            <div>
                <p class="text-slate-600 mb-4">Sélectionnez le montant à recharger :</p>

                <div id="recharge-amount-options" class="grid grid-cols-3 sm:grid-cols-5 gap-4 mb-6">
                    <?php $__currentLoopData = [10, 20, 50, 100, 200]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label
                            class="credit-option border-2 border-slate-200 rounded-lg p-3 text-center cursor-pointer hover:border-[#2ecc71] hover:bg-green-50 transition-all duration-200">
                            <input type="radio" name="recharge_amount" value="<?php echo e($amount); ?>" class="hidden">
                            <span class="text-xl font-bold text-gray-700"><?php echo e($amount); ?></span>
                            <span class="text-xs text-slate-500 block">crédits</span>
                        </label>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div id="payment-warning"
                    class="hidden bg-[#3b82f6] text-[#f1f8e9] p-3 my-4 rounded-lg text-sm text-center" role="alert">
                    <p><i class="fas fa-info-circle mr-2"></i>Ceci est une version TEST du projet ! Pour recharger votre
                        crédit, sélectionnez juste un montant et validez juste le paiement.</p>
                </div>

                <!-- Faux formulaire de paiement -->
                <div class="space-y-3 mt-4">
                    <input type="text" placeholder="Nom"
                        class="w-full p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed" readonly>
                    <input type="text" placeholder="Prénom"
                        class="w-full p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed" readonly>
                    <input type="text" placeholder="Numéro de carte de crédit"
                        class="w-full p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed" readonly>
                    <div class="flex gap-4">
                        <input type="text" placeholder="MM/AA"
                            class="flex-1 p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed"
                            readonly>
                        <input type="text" placeholder="CVC"
                            class="flex-1 p-2 border border-slate-300 rounded-md bg-slate-100 cursor-not-allowed"
                            readonly>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-6 flex justify-end space-x-4">
                <button type="button" onclick="closeAndResetRechargeModal()"
                    class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-all duration-200">Annuler</button>
                <button id="validate-payment-btn" disabled
                    class="px-4 py-2 bg-[#2ecc71] text-white font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 shadow-lg transition-all duration-300 disabled:bg-slate-300 disabled:cursor-not-allowed disabled:shadow-none hover:bg-[#27ae60]">
                    Valider le paiement
                </button>
            </div>
        </div>
    </div>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Ce script est maintenant beaucoup plus léger et ne contient que la logique spécifique à la page.

        // Gestion des messages de statut Laravel
        <?php if(session('status')): ?>
            document.addEventListener('DOMContentLoaded', function() {
                const status = '<?php echo e(session('status')); ?>';
                let message = '';

                switch (status) {
                    case 'trip-created':
                        message = 'Votre trajet a été créé avec succès !';
                        break;
                    case 'vehicle-added':
                        message = 'Véhicule ajouté avec succès !';
                        break;
                    case 'vehicle-updated':
                        message = 'Véhicule mis à jour avec succès !';
                        break;
                    case 'vehicle-deleted':
                        message = 'Véhicule supprimé avec succès !';
                        break;
                    case 'last-vehicle-deleted':
                        message = 'Dernier véhicule supprimé. Votre rôle a été changé en Passager.';
                        break;
                    case 'role-updated-to-driver':
                        message = 'Votre rôle a été mis à jour en Conducteur !';
                        break;
                    default:
                        // Vous pouvez ajouter un message par défaut si vous le souhaitez
                        // message = 'Action effectuée avec succès !';
                        break;
                }

                if (message) {
                    showSuccessNotification(message);
                }
            });
        <?php endif; ?>

        <?php if(session('error')): ?>
            document.addEventListener('DOMContentLoaded', function() {
                const errorMessage = '<?php echo e(session('error')); ?>';
                showErrorNotification(errorMessage);
            });
        <?php endif; ?>
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH /var/www/html/resources/views/dashboard.blade.php ENDPATH**/ ?>